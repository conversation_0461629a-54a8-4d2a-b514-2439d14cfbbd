import { Divider, Flex } from 'antd';
import styled from 'styled-components';
import { useDataState } from './store/useDataState';
import { useEffect } from 'react';
import LevelIcon, { getLevelIconUrl } from '@/components/level-icon';

const Container = styled.div`
  background: #fff;
  border-radius: 1.2em;
  width: 100%;
  padding: 1.6em;
  margin-bottom: 1.4em;
`;

// LabelContainer 支持显示等级图标
const LabelContainer = (props: { value: any; score_level: string }) => {
  const { value, score_level } = props;

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'contain',
        backgroundPosition: 'center',
        width: 100,
        fontWeight: 500,
        paddingLeft: 8,
        height: 37,
        lineHeight: '37px',
        fontSize: 16,
        flexShrink: 0,
      }}
    >
      <LevelIcon
        level={score_level}
        size={24}
        style={{
          marginRight: 4,
          flexShrink: 0,
        }}
      />
      {value}
    </div>
  );
};

const TargetWrapper = styled.div`
  text-align: center;
  &:first-child {
    justify-self: left;
  }
  &:nth-child(2) {
    justify-self: center;
  }
  &:nth-child(3) {
    justify-self: right;
  }
`;
const TargetItem = ({ label, value }) => {
  return (
    <TargetWrapper>
      <div style={{ color: '#999', fontSize: '1.4em' }}>{label}</div>
      <div
        style={{
          fontSize: '1.8em',
          fontWeight: 500,
          color: '#1a66ff',
        }}
      >
        {value || '-'}
      </div>
    </TargetWrapper>
  );
};
function ShopHeaderCard(props: {
  competivence: string;
  duration: string;
  score_level: string;
  situation: string;
}) {
  const { duration, score_level, situation, competivence } = props;
  const { shopName, shopList, isMultiShops, unregisterField, registerField, isShow } =
    useDataState();

  const shownFields = isShow?.(['competition', 'operatingStatus', 'potentialOpportunity']) || [
    true,
    true,
    true,
  ];

  // 多门店展示逻辑
  let displayShopName = shopName;
  if (shopList && shopList.length > 1) {
    const firstName = shopList[0]?.label || '';
    displayShopName = `${firstName}等 ${shopList.length}家门店`;
  }

  useEffect(() => {
    if (isMultiShops) {
      unregisterField('shopHeaderCard');
    } else {
      registerField({
        label: '店铺概览',
        key: 'shopHeaderCard',
        children: [
          {
            title: '同行竞争',
            key: 'competition',
          },
          {
            title: '经营现状',
            key: 'operatingStatus',
          },
          {
            title: '潜在商机',
            key: 'potentialOpportunity',
          },
        ],
      });
    }
  }, [isMultiShops]);

  return (
    <Container>
      <Flex wrap={false} align="center">
        <div style={{ fontSize: '1.6em', fontWeight: 500, marginRight: '1em' }}>
          {displayShopName}
        </div>
        {score_level && duration && !isMultiShops ? (
          <LabelContainer value={duration} score_level={score_level} />
        ) : null}
      </Flex>

      {isMultiShops ? null : (
        <>
          <Divider style={{ marginTop: '1em', marginBottom: '1em' }} />
          <div
            style={{ padding: '4px 12px', display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)' }}
          >
            {shownFields?.[0] && <TargetItem label="同行竞争" value={competivence || '-'} />}
            {shownFields?.[1] && <TargetItem label="经营现状" value={situation} />}
            {shownFields?.[2] && <TargetItem label="潜在商机" value="空间大" />}
          </div>
        </>
      )}
    </Container>
  );
}

export default ShopHeaderCard;
