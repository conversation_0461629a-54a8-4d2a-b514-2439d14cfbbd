import React, { useEffect, useState, useImperative<PERSON>andle, forwardRef } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import './style.less';
import { Flex, Spin, Button, Input, message } from 'antd';
import { FileMarkdownOutlined, EditOutlined } from '@ant-design/icons';
import { htmlToMarkdown, markdownToHtml, isMarkdownFormat } from './markdown-utils';

const MAX_TEXT_COUNT = 2000;

const formats = [
  'header',
  'bold',
  'italic',
  'underline',
  'strike',
  'blockquote',
  'list',
  'bullet',
  'indent',
  'image',
  'color',
];

const modules = {
  toolbar: [
    [{ header: [1, 2, 3, 4, 5, 6, 7, false] }],
    [
      'bold',
      'italic',
      'underline',
      'strike',
      {
        color: [],
      },
      'blockquote',
    ],
    [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }],
    ['image'],
  ],
};

export default forwardRef<
  {
    onInputBlur: () => void;
  },
  {
    fromAi?: boolean;
    value: string;
    onBlur: (val: string) => void;
    styles?: React.CSSProperties;
    extraButton?: React.ReactNode;
    loading?: boolean;
    enableMarkdown?: boolean; // 新增：是否启用 Markdown 功能
  }
>((props, ref) => {
  const { value, onBlur, styles = {}, extraButton, loading, fromAi, enableMarkdown = true } = props;
  const [localVal, setLocalVal] = useState(value);
  const [beyondLimits, setBeyondLimits] = useState(false);
  const [isMarkdownMode, setIsMarkdownMode] = useState(false);
  const [markdownContent, setMarkdownContent] = useState('');

  useImperativeHandle(ref, () => ({
    onInputBlur,
  }));

  function onInputChange(content, delta, source, editor) {
    // 这个方法拿到的长度有点问题，会比实际长度多1个字
    const currentTextCount = editor.getText()?.length;
    if (currentTextCount > MAX_TEXT_COUNT + 1) {
      setBeyondLimits(true);
    } else {
      setLocalVal(content);
      setBeyondLimits(false);
    }
  }

  const onInputBlur = () => {
    onBlur && onBlur(localVal);
  };

  // Markdown 模式切换
  const toggleMarkdownMode = () => {
    try {
      if (isMarkdownMode) {
        // 从 Markdown 模式切换到富文本模式
        const htmlContent = markdownToHtml(markdownContent);
        setLocalVal(htmlContent);
        setIsMarkdownMode(false);
        message.success('已切换到富文本模式');
      } else {
        // 从富文本模式切换到 Markdown 模式
        const markdown = htmlToMarkdown(localVal);
        setMarkdownContent(markdown);
        setIsMarkdownMode(true);
        message.success('已切换到 Markdown 模式');
      }
    } catch (error) {
      console.error('模式切换失败:', error);
      message.error('模式切换失败，请重试');
    }
  };

  // Markdown 内容变化处理
  const onMarkdownChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const content = e.target.value;
    setMarkdownContent(content);
    
    // 实时转换为 HTML 并更新
    try {
      const htmlContent = markdownToHtml(content);
      setLocalVal(htmlContent);
    } catch (error) {
      console.error('Markdown 转换失败:', error);
    }
  };

  useEffect(() => {
    setLocalVal(value);
    
    // 自动检测是否为 Markdown 格式
    if (enableMarkdown && value && isMarkdownFormat(value)) {
      setMarkdownContent(value);
      setIsMarkdownMode(true);
    }
  }, [value, enableMarkdown]);

  return (
    <Spin spinning={!!loading} tip="AI分析中">
      <div className="mp-rich-text-editor">
        <Flex className="title" align="center" justify="space-between">
          <Flex align="center">
            <img
              src="https://img.alicdn.com/imgextra/i4/O1CN01HT4TB51FdAdgpz0NN_!!6000000000509-2-tps-72-72.png"
              style={{ marginRight: 4, width: 20, height: 20 }}
            />
            AI智能分析
          </Flex>
          <Flex align="center" gap={8}>
            {enableMarkdown && (
              <Button
                type={isMarkdownMode ? 'primary' : 'default'}
                size="small"
                icon={isMarkdownMode ? <EditOutlined /> : <FileMarkdownOutlined />}
                onClick={toggleMarkdownMode}
                title={isMarkdownMode ? '切换到富文本模式' : '切换到 Markdown 模式'}
              >
                {isMarkdownMode ? '富文本' : 'Markdown'}
              </Button>
            )}
            {extraButton || null}
          </Flex>
        </Flex>
        <div
          style={
            fromAi
              ? {
                  position: 'relative',
                  background:
                    'linear-gradient(to bottom, rgba(240, 252, 255, 1), rgba(230, 248, 255, 0.5))',
                }
              : {}
          }
        >
          {fromAi && (
            <img
              style={{ position: 'absolute', top: 50, right: 0 }}
              src="https://img.alicdn.com/imgextra/i1/O1CN01KLrogl21sNLDRnz16_!!6000000007040-2-tps-156-142.png"
            />
          )}

          {isMarkdownMode ? (
            <Input.TextArea
              value={markdownContent}
              onChange={onMarkdownChange}
              onBlur={onInputBlur}
              placeholder="请输入 Markdown 内容..."
              autoSize={{ minRows: 8, maxRows: 20 }}
              style={{
                width: '100%',
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                fontSize: '14px',
                backgroundColor: fromAi ? '#ffffff52' : 'transparent',
                ...styles,
              }}
            />
          ) : (
            <ReactQuill
              theme="snow"
              value={localVal}
              onChange={onInputChange}
              onBlur={onInputBlur}
              formats={formats}
              modules={modules}
              style={{
                width: '100%',
                backgroundColor: fromAi ? '#ffffff52' : 'transparent',
                ...styles,
              }}
            />
          )}
        </div>
        {beyondLimits && <div className="error-text">{`最多输入${MAX_TEXT_COUNT}字`}</div>}
        {isMarkdownMode && (
          <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
            💡 支持标准 Markdown 语法：**粗体**、*斜体*、# 标题、- 列表、> 引用、`代码`、[链接](url) 等
          </div>
        )}
      </div>
    </Spin>
  );
});