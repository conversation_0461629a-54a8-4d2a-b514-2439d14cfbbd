import TurndownService from 'turndown';
import { marked } from 'marked';

// 配置 Turndown 服务（HTML 转 Markdown）
const turndownService = new TurndownService({
  headingStyle: 'atx',
  bulletListMarker: '-',
  codeBlockStyle: 'fenced',
  fence: '```',
  emDelimiter: '*',
  strongDelimiter: '**',
  linkStyle: 'inlined',
  linkReferenceStyle: 'full',
});

// 配置 marked（Markdown 转 HTML）
marked.setOptions({
  breaks: true,
  gfm: true,
});

/**
 * HTML 转 Markdown
 * @param html HTML 字符串
 * @returns Markdown 字符串
 */
export const htmlToMarkdown = (html: string): string => {
  if (!html) return '';
  try {
    return turndownService.turndown(html);
  } catch (error) {
    console.error('HTML 转 Markdown 失败:', error);
    return html;
  }
};

/**
 * Markdown 转 HTML
 * @param markdown Markdown 字符串
 * @returns HTML 字符串
 */
export const markdownToHtml = (markdown: string): string => {
  if (!markdown) return '';
  try {
    return marked(markdown) as string;
  } catch (error) {
    console.error('Markdown 转 HTML 失败:', error);
    return markdown;
  }
};

/**
 * 检测文本是否为 Markdown 格式
 * @param text 文本内容
 * @returns 是否为 Markdown 格式
 */
export const isMarkdownFormat = (text: string): boolean => {
  if (!text) return false;
  
  // 检测常见的 Markdown 语法
  const markdownPatterns = [
    /^#{1,6}\s+/m,           // 标题
    /\*\*.*?\*\*/,           // 粗体
    /\*.*?\*/,               // 斜体
    /~~.*?~~/,               // 删除线
    /^\s*[-*+]\s+/m,         // 无序列表
    /^\s*\d+\.\s+/m,         // 有序列表
    /^\s*>\s+/m,             // 引用
    /```[\s\S]*?```/,        // 代码块
    /`.*?`/,                 // 行内代码
    /\[.*?\]\(.*?\)/,        // 链接
    /!\[.*?\]\(.*?\)/,       // 图片
  ];
  
  return markdownPatterns.some(pattern => pattern.test(text));
};