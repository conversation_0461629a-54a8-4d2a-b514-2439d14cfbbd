import React from 'react';

// 等级图标映射
const LEVEL_ICONS = {
  LV1: 'https://img.alicdn.com/imgextra/i2/O1CN01YFO3Fq1OMzEOTvAHB_!!6000000001692-2-tps-63-63.png',
  LV2: 'https://img.alicdn.com/imgextra/i2/O1CN01hmiRG721NESSinYXk_!!6000000006972-2-tps-63-63.png',
  LV3: 'https://img.alicdn.com/imgextra/i2/O1CN01MEjBWI1pxA8laMQtx_!!6000000005426-2-tps-63-63.png',
  LV4: 'https://img.alicdn.com/imgextra/i2/O1CN01ZZHMd61hZKbd99t7t_!!6000000004291-2-tps-63-63.png',
  LV5: 'https://img.alicdn.com/imgextra/i1/O1CN01pjeChy1z8SRxDXP2n_!!6000000006669-2-tps-63-63.png',
  LV6: 'https://img.alicdn.com/imgextra/i4/O1CN01P3MInS1oUw7DZo6HH_!!6000000005229-2-tps-63-63.png',
};

export interface LevelIconProps {
  level?: string; // 等级
  size?: number; // 图标大小
  style?: React.CSSProperties; // 自定义
  alt?: string;
  className?: string;
}

/**
 * 等级图标组件
 */
const LevelIcon: React.FC<LevelIconProps> = ({ level, size = 18, style, alt, className }) => {
  const getLevelIconUrl = (scoreLevel?: string) => {
    if (!scoreLevel) return LEVEL_ICONS.LV1;
    const levelKey = scoreLevel.toUpperCase() as keyof typeof LEVEL_ICONS;
    return LEVEL_ICONS[levelKey] || LEVEL_ICONS.LV1;
  };

  const iconUrl = getLevelIconUrl(level);
  const altText = alt || `等级${level || 'LV1'}`;

  return (
    <img
      src={iconUrl}
      alt={altText}
      className={className}
      style={{
        width: size,
        height: size,
        verticalAlign: 'middle',
        ...style,
      }}
    />
  );
};

/**
 * @param level 等级字符串
 * @returns 图标 URL
 */
export const getLevelIconUrl = (level?: string): string => {
  if (!level) return LEVEL_ICONS.LV1;
  const levelKey = level.toUpperCase() as keyof typeof LEVEL_ICONS;
  return LEVEL_ICONS[levelKey] || LEVEL_ICONS.LV1;
};

/**
 * 等级图标映射常量
 */
export const LEVEL_ICON_MAP = LEVEL_ICONS;

export default LevelIcon;
